"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_GoogleSignInButton_js";
exports.ids = ["components_GoogleSignInButton_js"];
exports.modules = {

/***/ "./components/GoogleSignInButton.js":
/*!******************************************!*\
  !*** ./components/GoogleSignInButton.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoogleSignInButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_deviceId__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/deviceId */ \"./lib/deviceId.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_deviceId__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_deviceId__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction GoogleSignInButton() {\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;\n        if (!clientId) return;\n        // load GIS script\n        const s = document.createElement(\"script\");\n        s.src = \"https://accounts.google.com/gsi/client\";\n        s.async = true;\n        s.defer = true;\n        s.onload = ()=>{\n            /* global google */ window.google?.accounts.id.initialize({\n                client_id: clientId,\n                callback: async (resp)=>{\n                    const idToken = resp.credential;\n                    const deviceId = (0,_lib_deviceId__WEBPACK_IMPORTED_MODULE_2__.getDeviceId)();\n                    const r = await fetch(`${process.env.NEXT_PUBLIC_AUTH_API}/api/google-login`, {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        credentials: \"include\",\n                        body: JSON.stringify({\n                            idToken,\n                            deviceId\n                        })\n                    });\n                    if (r.ok) window.location.href = \"/dashboard\";\n                    else alert(\"Google login failed\");\n                }\n            });\n            window.google?.accounts.id.renderButton(divRef.current, {\n                theme: \"outline\",\n                size: \"large\"\n            });\n        };\n        document.body.appendChild(s);\n        return ()=>{\n            document.body.removeChild(s);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: divRef\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\components\\\\GoogleSignInButton.js\",\n        lineNumber: 38,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/GoogleSignInButton.js\n");

/***/ })

};
;