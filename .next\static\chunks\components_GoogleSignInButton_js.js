"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_GoogleSignInButton_js"],{

/***/ "./components/GoogleSignInButton.js":
/*!******************************************!*\
  !*** ./components/GoogleSignInButton.js ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GoogleSignInButton; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_deviceId__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/deviceId */ \"./lib/deviceId.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction GoogleSignInButton() {\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;\n        if (!clientId) return;\n        // load GIS script\n        const s = document.createElement(\"script\");\n        s.src = \"https://accounts.google.com/gsi/client\";\n        s.async = true;\n        s.defer = true;\n        s.onload = ()=>{\n            var /* global google */ _window_google, _window_google1;\n            (_window_google = window.google) === null || _window_google === void 0 ? void 0 : _window_google.accounts.id.initialize({\n                client_id: clientId,\n                callback: async (resp)=>{\n                    const idToken = resp.credential;\n                    const deviceId = (0,_lib_deviceId__WEBPACK_IMPORTED_MODULE_2__.getDeviceId)();\n                    const r = await fetch(\"\".concat(process.env.NEXT_PUBLIC_AUTH_API, \"/api/google-login\"), {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        credentials: \"include\",\n                        body: JSON.stringify({\n                            idToken,\n                            deviceId\n                        })\n                    });\n                    if (r.ok) window.location.href = \"/dashboard\";\n                    else alert(\"Google login failed\");\n                }\n            });\n            (_window_google1 = window.google) === null || _window_google1 === void 0 ? void 0 : _window_google1.accounts.id.renderButton(divRef.current, {\n                theme: \"outline\",\n                size: \"large\"\n            });\n        };\n        document.body.appendChild(s);\n        return ()=>{\n            document.body.removeChild(s);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: divRef\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\components\\\\GoogleSignInButton.js\",\n        lineNumber: 38,\n        columnNumber: 10\n    }, this);\n}\n_s(GoogleSignInButton, \"yu+j3H3uMpPkc7UDcPsbvSQj1vE=\");\n_c = GoogleSignInButton;\nvar _c;\n$RefreshReg$(_c, \"GoogleSignInButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/GoogleSignInButton.js\n"));

/***/ })

}]);