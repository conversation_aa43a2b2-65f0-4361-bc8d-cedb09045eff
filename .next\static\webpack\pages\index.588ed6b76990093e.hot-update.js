"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.js":
/*!************************!*\
  !*** ./pages/index.js ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_deviceId__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/deviceId */ \"./lib/deviceId.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst GoogleSignInButton = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"components_GoogleSignInButton_js\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/GoogleSignInButton */ \"./components/GoogleSignInButton.js\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\index.js -> \" + \"../components/GoogleSignInButton\"\n        ]\n    },\n    ssr: false\n});\n_c = GoogleSignInButton;\nfunction Home() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"<EMAIL>\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deviceId, setDeviceId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n        setDeviceId((0,_lib_deviceId__WEBPACK_IMPORTED_MODULE_2__.getDeviceId)());\n    }, []);\n    async function onRegister(e) {\n        e.preventDefault();\n        const res = await fetch(\"\".concat(process.env.NEXT_PUBLIC_AUTH_API, \"/api/register\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n        if (res.ok) alert(\"Đăng k\\xfd th\\xe0nh c\\xf4ng. H\\xe3y đăng nhập.\");\n        else alert(\"Đăng k\\xfd thất bại\");\n    }\n    async function onLogin(e) {\n        e.preventDefault();\n        console.log(\"Logging in...\", process.env.NEXT_PUBLIC_AUTH_API);\n        const res = await fetch(\"\".concat(process.env.NEXT_PUBLIC_AUTH_API, \"/api/login\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\",\n            body: JSON.stringify({\n                email,\n                password,\n                deviceId\n            })\n        });\n        if (res.ok) window.location.href = \"/dashboard\";\n        else {\n            const t = await res.json().catch(()=>({\n                    error: \"\"\n                }));\n            alert(\"Login failed: \" + (t.error || \"\"));\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        style: {\n            maxWidth: 420,\n            margin: \"60px auto\",\n            fontFamily: \"sans-serif\",\n            display: \"grid\",\n            gap: 16\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Auth Demo\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: onRegister,\n                style: {\n                    display: \"grid\",\n                    gap: 8\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Đăng k\\xfd (email/password)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        placeholder: \"email\",\n                        value: email,\n                        onChange: (e)=>setEmail(e.target.value)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        placeholder: \"password\",\n                        type: \"password\",\n                        value: password,\n                        onChange: (e)=>setPassword(e.target.value)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        children: \"Register\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: onLogin,\n                style: {\n                    display: \"grid\",\n                    gap: 8\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Đăng nhập (email/password)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        placeholder: \"email\",\n                        value: email,\n                        onChange: (e)=>setEmail(e.target.value)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        placeholder: \"password\",\n                        type: \"password\",\n                        value: password,\n                        onChange: (e)=>setPassword(e.target.value)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        children: \"Login\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        style: {\n                            opacity: 0.7\n                        },\n                        children: [\n                            \"Device: \",\n                            isClient ? deviceId : \"\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Hoặc đăng nhập bằng Google\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GoogleSignInButton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\jwt-sessionversion-mongo-google-skeleton\\\\web\\\\pages\\\\index.js\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"74+kyKfTfq5t/gJcyEfpqkIVflA=\");\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"GoogleSignInButton\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.js\n"));

/***/ })

});