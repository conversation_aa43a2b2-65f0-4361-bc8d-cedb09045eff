import { useState, useEffect } from "react";
import { getDeviceId } from "../lib/deviceId";
import dynamic from "next/dynamic";
const GoogleSignInButton = dynamic(
  () => import("../components/GoogleSignInButton"),
  { ssr: false }
);

export default function Home() {
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("");
  const [deviceId, setDeviceId] = useState("");
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    setDeviceId(getDeviceId());
  }, []);

  async function onRegister(e) {
    e.preventDefault();
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_AUTH_API}/api/register`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      }
    );
    if (res.ok) alert("<PERSON><PERSON>ng ký thành công. Hãy đăng nhập.");
    else alert("Đăng ký thất bại");
  }

  async function onLogin(e) {
    e.preventDefault();
    const res = await fetch(`${process.env.NEXT_PUBLIC_AUTH_API}/api/login`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      credentials: "include",
      body: JSON.stringify({ email, password, deviceId }),
    });
    if (res.ok) window.location.href = "/dashboard";
    else {
      const t = await res.json().catch(() => ({ error: "" }));
      alert("Login failed: " + (t.error || ""));
    }
  }

  return (
    <main
      style={{
        maxWidth: 420,
        margin: "60px auto",
        fontFamily: "sans-serif",
        display: "grid",
        gap: 16,
      }}
    >
      <h1>Auth Demo</h1>
      <form onSubmit={onRegister} style={{ display: "grid", gap: 8 }}>
        <h3>Đăng ký (email/password)</h3>
        <input
          placeholder="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
        <input
          placeholder="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
        <button type="submit">Register</button>
      </form>

      <form onSubmit={onLogin} style={{ display: "grid", gap: 8 }}>
        <h3>Đăng nhập (email/password)</h3>
        <input
          placeholder="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
        <input
          placeholder="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
        <button type="submit">Login</button>
        <small style={{ opacity: 0.7 }}>
          Device: {isClient ? deviceId : ""}
        </small>
      </form>

      <div>
        <h3>Hoặc đăng nhập bằng Google</h3>
        <GoogleSignInButton />
      </div>
    </main>
  );
}
