'use client';
import { useEffect } from "react";

export function useAutoRefresh() {
  useEffect(() => {
    let timer;
    async function tick() {
      try { await fetch(`${process.env.NEXT_PUBLIC_AUTH_API}/api/refresh`, { method: "POST", credentials: "include" }); } catch {}
      schedule();
    }
    function schedule() {
      const seconds = 240;
      timer = setTimeout(tick, seconds * 1000);
    }
    schedule();
    return () => clearTimeout(timer);
  }, []);
}
