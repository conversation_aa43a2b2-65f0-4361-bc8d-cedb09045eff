'use client';
import { useEffect } from "react";
import { io } from "socket.io-client";

export function useForceLogout(userId) {
  useEffect(() => {
    if (!userId) return;
    const socket = io(process.env.NEXT_PUBLIC_AUTH_WS_URL, { withCredentials: true });
    socket.on("connect", () => socket.emit("join", userId));
    socket.on("force-logout", async () => {
      try { await fetch(`${process.env.NEXT_PUBLIC_AUTH_API}/api/logout`, { method: "POST", credentials: "include" }); } catch {}
      window.location.href = "/?reason=conflict";
    });
    return () => socket.disconnect();
  }, [userId]);
}
