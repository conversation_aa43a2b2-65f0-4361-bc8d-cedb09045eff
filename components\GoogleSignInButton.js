'use client';
import { useEffect, useRef } from "react";
import { getDeviceId } from "../lib/deviceId";

export default function GoogleSignInButton() {
  const divRef = useRef(null);
  useEffect(() => {
    const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
    if (!clientId) return;
    // load GIS script
    const s = document.createElement("script");
    s.src = "https://accounts.google.com/gsi/client";
    s.async = true;
    s.defer = true;
    s.onload = () => {
      /* global google */
      window.google?.accounts.id.initialize({
        client_id: clientId,
        callback: async (resp) => {
          const idToken = resp.credential;
          const deviceId = getDeviceId();
          const r = await fetch(`${process.env.NEXT_PUBLIC_AUTH_API}/api/google-login`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            credentials: "include",
            body: JSON.stringify({ idToken, deviceId })
          });
          if (r.ok) window.location.href = "/dashboard";
          else alert("Google login failed");
        }
      });
      window.google?.accounts.id.renderButton(divRef.current, { theme: "outline", size: "large" });
    };
    document.body.appendChild(s);
    return () => { document.body.removeChild(s); };
  }, []);

  return <div ref={divRef} />;
}
